"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/he-thong/chung-tu/cau-truc-quyen-so-chung-tu/page",{

/***/ "(app-pages-browser)/./src/hooks/queries/useQuyenChungTu.ts":
/*!**********************************************!*\
  !*** ./src/hooks/queries/useQuyenChungTu.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuyenChungTu: function() { return /* binding */ useQuyenChungTu; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@13.5.8_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react-experimental/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants */ \"(app-pages-browser)/./src/constants/index.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\n\n\nconst useQuyenChungTu = function() {\n    let { initialData = [], pageSize = 10 } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const [quyenChungTus, setQuyenChungTu] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialData);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [totalItems, setTotalItems] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const { entity } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const fetchQuyenChungTu = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0;\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) return;\n        setIsLoading(true);\n        try {\n            const params = {\n                page: page + 1,\n                page_size: pageSize\n            };\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/entities/\".concat(entity.slug, \"/erp/\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.QUERY_KEYS.QUYEN_CHUNG_TU, \"/\"), {\n                params\n            });\n            const mappedData = response.data.results;\n            setQuyenChungTu(mappedData);\n            setTotalItems(response.data.count || 0);\n            setCurrentPage(page);\n        } catch (error) {\n            console.error(\"Error fetching document books:\", error);\n            setQuyenChungTu([]);\n            setTotalItems(0);\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        entity === null || entity === void 0 ? void 0 : entity.slug,\n        pageSize\n    ]);\n    const addQuyenChungTu = async (newQuyenChungTu)=>{\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) return;\n        setIsLoading(true);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/entities/\".concat(entity.slug, \"/erp/\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.QUERY_KEYS.QUYEN_CHUNG_TU, \"/\"), newQuyenChungTu);\n            // Add the new quyenChungTu to the list\n            const newQuyenChungTuData = response.data;\n            setQuyenChungTu((prev)=>[\n                    ...prev,\n                    newQuyenChungTuData\n                ]);\n            await fetchQuyenChungTu(currentPage);\n        } catch (error) {\n            console.error(\"Error adding document book:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateQuyenChungTu = async (uuid, updatedQuyenChungTu)=>{\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) return;\n        setIsLoading(true);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put(\"/entities/\".concat(entity.slug, \"/erp/\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.QUERY_KEYS.QUYEN_CHUNG_TU, \"/\").concat(uuid, \"/\"), {\n                ma_nk: updatedQuyenChungTu.ma_nk,\n                ten_nk: updatedQuyenChungTu.ten_nk,\n                so_ct_mau: updatedQuyenChungTu.so_ct_mau\n            });\n            // Update the quyenChungTu in the list\n            const updatedQuyenChungTuData = response.data;\n            setQuyenChungTu((prev)=>prev.map((quyenChungTu)=>quyenChungTu.uuid === updatedQuyenChungTuData.uuid ? updatedQuyenChungTuData : quyenChungTu));\n            await fetchQuyenChungTu(currentPage);\n        } catch (error) {\n            console.error(\"Error updating document book:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const deleteQuyenChungTu = async (uuid)=>{\n        if (!(entity === null || entity === void 0 ? void 0 : entity.slug)) return;\n        setIsLoading(true);\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_3__[\"default\"].delete(\"/entities/\".concat(entity.slug, \"/erp/\").concat(_constants__WEBPACK_IMPORTED_MODULE_2__.QUERY_KEYS.QUYEN_CHUNG_TU, \"/\").concat(uuid, \"/\"));\n            // Remove the quyenChungTu from the list\n            setQuyenChungTu((prev)=>prev.filter((quyenChungTu)=>quyenChungTu.uuid !== uuid));\n            // Refresh the current page to show the updated list\n            await fetchQuyenChungTu(currentPage);\n        } catch (error) {\n            console.error(\"Error deleting document book:\", error);\n            throw error;\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handlePageChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (page)=>{\n        await fetchQuyenChungTu(page);\n    }, [\n        fetchQuyenChungTu\n    ]);\n    const refreshQuyenChungTu = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        await fetchQuyenChungTu(currentPage);\n    }, [\n        fetchQuyenChungTu,\n        currentPage\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        fetchQuyenChungTu();\n    }, [\n        fetchQuyenChungTu\n    ]);\n    return {\n        quyenChungTus,\n        isLoading,\n        totalItems,\n        currentPage,\n        handlePageChange,\n        addQuyenChungTu,\n        updateQuyenChungTu,\n        deleteQuyenChungTu,\n        refreshQuyenChungTu\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/queries/useQuyenChungTu.ts\n"));

/***/ })

});