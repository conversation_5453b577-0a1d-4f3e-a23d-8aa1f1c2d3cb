import { Button } from '@mui/material';
import { useState } from 'react';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoIcon } from '@/components/custom/arito/icon';

interface DeleteDialogProps {
  open: boolean;
  onClose: () => void;
  selectedObj: any | null;
  clearSelection?: () => void;
  onDelete?: (uuid: string) => Promise<void>;
}

function DeleteDialog({ onClose, open, selectedObj, clearSelection, onDelete }: DeleteDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleDelete = async () => {
    if (!selectedObj || !onDelete) return;

    setIsDeleting(true);
    setError(null);

    try {
      await onDelete(selectedObj.uuid);

      if (clearSelection) {
        clearSelection();
      }
      onClose();
    } catch (err: any) {
      console.error('Error deleting document book:', err);
      setError(err.message || 'Lỗi khi xóa quyển chứng từ');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Xóa dữ liệu'
      maxWidth='lg'
      disableBackdropClose={true}
      disableEscapeKeyDown={true}
      titleIcon={<AritoIcon icon={260} />}
      actions={
        <>
          <Button
            className='bg-[rgba(15,118,110,0.9)] text-white hover:bg-[rgba(15,118,110,1)]'
            onClick={handleDelete}
            type='submit'
            disabled={isDeleting}
            variant='contained'
          >
            <AritoIcon icon={884} marginX='4px' />
            {isDeleting ? 'Đang xóa...' : 'Đồng ý'}
          </Button>
          <Button onClick={onClose} variant='outlined' disabled={isDeleting}>
            <AritoIcon icon={885} marginX='4px' />
            Huỷ
          </Button>
        </>
      }
    >
      <div className='min-w-[40vw] p-4'>
        <p className='text-base font-medium'>Bạn có chắc chắn muốn xóa không?</p>
        {error && <div className='mt-4 rounded bg-red-100 p-2 text-red-700'>{error}</div>}
      </div>
    </AritoDialog>
  );
}

export default DeleteDialog;
